<template>
  <a-modal
    :title="title"
    :width="width"
    :open="visible"
    @cancel="handleCancel"
    :destroyOnClose="true"
    :centered="false"
    :mask-closable="false"
    wrap-class-name="fullscreen-modal"
  >
    <template #footer>
      <!-- 操作按钮组和图片展示 -->
      <div class="action-buttons">
        <!-- 操作按钮行 -->
        <a-space>
          <!-- 智能填写按钮 -->
          <!--          <a-button v-if="!disableSubmit" type="dashed" @click="handleSmartFill" :title="'基于历史问卷智能填写'">
            <template #icon>
              <thunderbolt-outlined />
            </template>
            智能填写
          </a-button>-->

          <!-- 现场拍照按钮 -->
          <a-button v-if="!disableSubmit" type="dashed" @click="handleTakePhoto" :title="'现场拍照'">
            <template #icon>
              <camera-outlined />
            </template>
            现场拍照
          </a-button>

          <!-- 签字按钮 -->
          <a-button v-if="!disableSubmit" type="dashed" @click="handleSign" :title="'电子签名'" :disabled="!signBoardConnected">
            <template #icon>
              <edit-outlined />
            </template>
            {{ signBoardConnected ? '电子签名' : '签字板未连接' }}
          </a-button>

          <!-- 历史问卷按钮 -->
          <!--          <a-button @click="handleShowHistory" :title="'查看历史问卷记录'">
            <template #icon>
              <history-outlined />
            </template>
            历史问卷
          </a-button>-->

          <!-- 预览按钮 -->
          <a-button type="dashed" @click="handlePreviewQuestionnaire" :title="disableSubmit ? '预览问卷' : '预览当前填写内容'">
            <template #icon>
              <file-text-outlined />
            </template>
            预览问卷
          </a-button>

          <!-- 完成问卷按钮 -->
          <a-button
            v-if="!disableSubmit && !isCompleted"
            type="primary"
            @click="handleCompleteQuestionnaire"
            :title="'完成问卷并锁定'"
            :loading="completingQuestionnaire"
          >
            <template #icon>
              <check-circle-outlined />
            </template>
            完成问卷
          </a-button>

          <!-- 重新编辑按钮 -->
          <a-button
            v-if="!disableSubmit && isCompleted"
            type="default"
            @click="handleReopenQuestionnaire"
            :title="'重新编辑问卷'"
            :loading="reopeningQuestionnaire"
          >
            <template #icon>
              <edit-outlined />
            </template>
            重新编辑
          </a-button>
        </a-space>
      </div>
    </template>
    <div class="comprehensive-inquiry fullscreen-content">
      <!-- 客户基本信息展示 -->
      <div v-if="currentCustomerReg" class="customer-info">
        <div class="customer-avatar">
          <div
            class="avatar"
            :style="{ backgroundImage: `url(${currentCustomerReg.avatar ? getFileAccessHttpUrl(currentCustomerReg.avatar) : defaultAvatar})` }"
            @click="previewAvatar"
          ></div>
        </div>
        <div class="info">
          <h3>{{ currentCustomerReg.name }}</h3>
          <div class="details">
            <span><span class="detail-label">性别：</span>{{ currentCustomerReg.gender }}</span>
            <span><span class="detail-label">年龄：</span>{{ currentCustomerReg.age }}{{ currentCustomerReg.ageUnit }}</span>
            <span><span class="detail-label">电话：</span>{{ currentCustomerReg.phone }}</span>
            <span><span class="detail-label">体检号：</span>{{ currentCustomerReg.customerNo || '-' }}</span>
            <span><span class="detail-label">身份证：</span>{{ currentCustomerReg.idCard || '-' }}</span>
          </div>
        </div>
        <div class="status">
          <div class="status-indicator" :class="{ readonly: disableSubmit, completed: isCompleted }"></div>
          <span>{{ getStatusText() }}</span>
          <a-tag v-if="isCompleted" color="success" style="margin-left: 8px">
            <template #icon>
              <check-circle-outlined />
            </template>
            已完成
          </a-tag>
          <a-tag v-else color="processing" style="margin-left: 8px">
            <template #icon>
              <edit-outlined />
            </template>
            {{ disableSubmit ? '查看中' : '编辑中' }}
          </a-tag>
        </div>
      </div>
      <!-- 只读模式或已完成状态下显示问卷内容 -->
      <div v-if="isCompleted || readonly" class="readonly-content">
        <QuestionnaireDisplayContent ref="readonlyContentRef" :inquiryId="formData.id" :embedded="true" />
      </div>
      <div class="tab-container" v-else>
        <!-- 编辑模式下显示tabs -->
        <a-tabs v-model:activeKey="activeTabKey" tab-position="left" :tab-bar-style="{ width: '32px' }" class="vertical-tabs">
          <!-- 第一个tab：基本情况 -->
          <a-tab-pane key="basic" class="vertical-tab-title">
            <template #tab>
              <div class="vertical-tab-label-optimized">基本情况</div>
            </template>

            <div class="tab-content">
              <a-form ref="formRef" :model="formData" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-card title="🚬 烟酒情况" size="small" class="form-card">
                  <a-row>
                    <a-col :span="8">
                      <a-form-item label="吸烟状态" v-bind="validateInfos.smokStatus">
                        <j-dict-select-tag
                          v-model:value="formData.smokStatus"
                          dictCode="smoke_status"
                          placeholder="请选择吸烟状态"
                          :disabled="disableSubmit"
                          :auto-set-default="true"
                          default-index="0"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="一天多少支" v-bind="validateInfos.smokAmount">
                        <a-input v-model:value="formData.smokAmount" placeholder="请输入支数" :disabled="disableSubmit" suffix="支" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="吸烟多少年" v-bind="validateInfos.smokYears">
                        <a-input v-model:value="formData.smokYears" placeholder="请输入年数" :disabled="disableSubmit" suffix="年" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="饮酒状态" v-bind="validateInfos.drinkStatus">
                        <j-dict-select-tag
                          v-model:value="formData.drinkStatus"
                          dictCode="drink_status"
                          placeholder="请选择饮酒状态"
                          :disabled="disableSubmit"
                          :auto-set-default="true"
                          default-index="0"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="一天饮酒量" v-bind="validateInfos.drinkAmount">
                        <a-input v-model:value="formData.drinkAmount" placeholder="请输入饮酒量" :disabled="disableSubmit" suffix="ml" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="饮酒多少年" v-bind="validateInfos.drinkYears">
                        <a-input v-model:value="formData.drinkYears" placeholder="请输入年数" :disabled="disableSubmit" suffix="年" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </a-card>

                <!-- 月经史 - 仅女性显示 -->
                <a-card v-if="isFemale" title="🌸 月经史" size="small" class="form-card">
                  <a-row>
                    <a-col :span="6">
                      <a-form-item label="月经初潮">
                        <a-input-number
                          v-model:value="formData.menarche"
                          placeholder="年龄"
                          style="width: 100%"
                          :disabled="disableSubmit"
                          :min="8"
                          :max="20"
                          suffix="岁"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="6">
                      <a-form-item label="经期">
                        <a-input-number
                          v-model:value="formData.menstruation"
                          placeholder="天数"
                          style="width: 100%"
                          :disabled="disableSubmit"
                          :min="1"
                          :max="15"
                          suffix="天"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="6">
                      <a-form-item label="周期">
                        <a-input v-model:value="formData.period" placeholder="周期天数" :disabled="disableSubmit" suffix="天" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="6">
                      <a-form-item label="停经年龄">
                        <a-input-number
                          v-model:value="formData.menopauseAge"
                          placeholder="年龄"
                          style="width: 100%"
                          :disabled="disableSubmit"
                          :min="35"
                          :max="65"
                          suffix="岁"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </a-card>

                <!-- 生育史 -->
                <a-card title="👶 生育史" size="small" class="form-card">
                  <a-row>
                    <a-col :span="8">
                      <a-form-item label="现有子女个数">
                        <a-input-number
                          v-model:value="formData.childCount"
                          placeholder="子女个数"
                          style="width: 100%"
                          :disabled="disableSubmit"
                          :min="0"
                          :max="20"
                          suffix="个"
                        />
                      </a-form-item>
                    </a-col>
                    <!-- 以下字段仅女性显示 -->
                    <a-col v-if="isFemale" :span="8">
                      <a-form-item label="流产次数">
                        <a-input-number
                          v-model:value="formData.abortionCount"
                          placeholder="流产次数"
                          style="width: 100%"
                          :disabled="disableSubmit"
                          :min="0"
                          :max="20"
                          suffix="次"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col v-if="isFemale" :span="8">
                      <a-form-item label="早产次数">
                        <a-input-number
                          v-model:value="formData.prematureCount"
                          placeholder="早产次数"
                          style="width: 100%"
                          :disabled="disableSubmit"
                          :min="0"
                          :max="20"
                          suffix="次"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col v-if="isFemale" :span="8">
                      <a-form-item label="死胎次数">
                        <a-input-number
                          v-model:value="formData.stillbirth"
                          placeholder="死胎次数"
                          style="width: 100%"
                          :disabled="disableSubmit"
                          :min="0"
                          :max="20"
                          suffix="次"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col v-if="isFemale" :span="8">
                      <a-form-item label="异常胎次数">
                        <a-input-number
                          v-model:value="formData.abnormalfetal"
                          placeholder="异常胎次数"
                          style="width: 100%"
                          :disabled="disableSubmit"
                          :min="0"
                          :max="20"
                          suffix="次"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col v-if="isFemale" :span="8">
                      <a-form-item label="怀孕次数">
                        <a-input-number
                          v-model:value="formData.pregnancy"
                          placeholder="怀孕次数"
                          style="width: 100%"
                          :disabled="disableSubmit"
                          :min="0"
                          :max="20"
                          suffix="次"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="先天畸形情况">
                        <a-textarea
                          v-model:value="formData.congenitalMalformations"
                          placeholder="请详细描述先天畸形情况，如无则填'无'"
                          :disabled="disableSubmit"
                          :rows="2"
                          show-count
                          :maxlength="200"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </a-card>

                <!-- 保存按钮 -->
                <div class="form-actions inquiry-actions">
                  <a-space>
                    <a-button type="primary" @click="handleSubmit" :loading="submitting" size="large">
                      <template #icon>
                        <i class="anticon anticon-save"></i>
                      </template>
                      保存基本信息
                    </a-button>
                  </a-space>
                </div>
              </a-form>
            </div>
          </a-tab-pane>

          <!-- 第三个tab：症状 -->
          <a-tab-pane key="symptoms">
            <template #tab>
              <div class="vertical-tab-label-optimized">症状</div>
            </template>
            <div class="tab-content">
              <SymptomList :disabled="disableSubmit" />
            </div>
          </a-tab-pane>
          <!-- 第四个tab：职业史 -->
          <a-tab-pane key="occupation">
            <template #tab>
              <div class="vertical-tab-label-optimized">职业史</div>
            </template>
            <div class="tab-content">
              <OccupationalHistoryList ref="occupationalHistoryRef" :disabled="disableSubmit" />
            </div>
          </a-tab-pane>
          <!-- 第六个tab：放射史 -->
          <a-tab-pane key="radiation">
            <template #tab>
              <div class="vertical-tab-label-optimized">放射史</div>
            </template>
            <div class="tab-content">
              <RadiationHistoryList ref="radiationHistoryRef" :disabled="disableSubmit" />
            </div>
          </a-tab-pane>
          <!-- 第五个tab：既往病史 -->
          <a-tab-pane key="disease">
            <template #tab>
              <div class="vertical-tab-label-optimized">既往病史</div>
            </template>
            <div class="tab-content">
              <DiseaseHistoryList ref="diseaseHistoryRef" :disabled="disableSubmit" />
            </div>
          </a-tab-pane>
          <!-- 第七个tab：家族史 -->
          <a-tab-pane key="family">
            <template #tab>
              <div class="vertical-tab-label-optimized">家族史</div>
            </template>
            <div class="tab-content">
              <FamilyHistoryList ref="familyHistoryRef" :disabled="disableSubmit" />
            </div>
          </a-tab-pane>
          <!-- 第二个tab：婚姻状况 -->
          <a-tab-pane key="marital">
            <template #tab>
              <div class="vertical-tab-label-optimized">婚姻状况</div>
            </template>
            <div class="tab-content">
              <MaritalStatusList ref="maritalStatusRef" :disabled="disableSubmit" />
            </div>
          </a-tab-pane>

          <!-- 现场记录tab：拍照和签字 -->
          <a-tab-pane key="records">
            <template #tab>
              <div class="vertical-tab-label-optimized">现场记录</div>
            </template>
            <div class="tab-content">
              <div class="records-content">
                <!-- 操作按钮区域 -->
                <div class="records-actions">
                  <a-space size="large">
                    <!-- 拍照操作区 -->
                    <a-card title="📷 现场拍照" size="small" class="action-card">
                      <div class="action-content">
                        <div v-if="!formData.answerPicture" class="empty-state">
                          <camera-outlined style="font-size: 48px; color: #d9d9d9; margin-bottom: 16px" />
                          <p style="color: #999; margin-bottom: 16px">尚未拍照</p>
                          <a-button type="primary" @click="handleTakePhoto" :disabled="disableSubmit" size="large">
                            <template #icon>
                              <camera-outlined />
                            </template>
                            开始拍照
                          </a-button>
                        </div>
                        <div v-else class="photo-display">
                          <a-image
                            :src="getFileAccessHttpUrl(formData.answerPicture)"
                            :fallback="defaultAvatar"
                            :preview="true"
                            class="record-image"
                          />
                          <div class="image-info">
                            <p style="margin: 8px 0 16px 0; color: #666">拍照时间：{{ getCurrentTime() }}</p>
                            <a-space v-if="!disableSubmit">
                              <a-button @click="handleTakePhoto">
                                <template #icon>
                                  <camera-outlined />
                                </template>
                                重新拍照
                              </a-button>
                              <a-button danger @click="handleDeletePhoto">
                                <template #icon>
                                  <delete-outlined />
                                </template>
                                删除照片
                              </a-button>
                            </a-space>
                          </div>
                        </div>
                      </div>
                    </a-card>

                    <!-- 签字操作区 -->
                    <a-card title="✍️ 电子签名" size="small" class="action-card">
                      <div class="action-content">
                        <div v-if="!formData.signPicture" class="empty-state">
                          <edit-outlined style="font-size: 48px; color: #d9d9d9; margin-bottom: 16px" />
                          <p style="color: #999; margin-bottom: 8px">尚未签名</p>
                          <p style="color: #999; font-size: 12px; margin-bottom: 16px">
                            签字板状态：
                            <a-tag :color="signBoardConnected ? 'green' : 'red'">
                              {{ signBoardConnected ? '已连接' : '未连接' }}
                            </a-tag>
                          </p>
                          <a-button type="primary" @click="handleSign" :disabled="disableSubmit || !signBoardConnected" size="large">
                            <template #icon>
                              <edit-outlined />
                            </template>
                            {{ signBoardConnected ? '开始签名' : '签字板未连接' }}
                          </a-button>
                        </div>
                        <div v-else class="signature-display">
                          <a-image
                            :src="getFileAccessHttpUrl(formData.signPicture)"
                            :fallback="defaultAvatar"
                            :preview="true"
                            class="record-image signature-image"
                          />
                          <div class="image-info">
                            <p style="margin: 8px 0 16px 0; color: #666">签名时间：{{ getCurrentTime() }}</p>
                            <a-space v-if="!disableSubmit">
                              <a-button @click="handleSign" :disabled="!signBoardConnected">
                                <template #icon>
                                  <edit-outlined />
                                </template>
                                重新签名
                              </a-button>
                              <a-button danger @click="handleDeleteSignature">
                                <template #icon>
                                  <delete-outlined />
                                </template>
                                删除签名
                              </a-button>
                            </a-space>
                          </div>
                        </div>
                      </div>
                    </a-card>
                  </a-space>
                </div>

                <!-- 签字板状态显示 -->
                <div class="signboard-status" v-if="!disableSubmit">
                  <a-card title="🔧 设备状态" size="small" style="margin-top: 20px">
                    <SignBoard ref="signBoardRef" @sign-done="handleSignDone" @connected="handleSignBoardConnected" />
                  </a-card>
                </div>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>

    <!-- 问卷预览弹窗 -->
    <QuestionnaireDisplayModal ref="questionnaireDisplayRef" @success="handlePreviewSuccess" />

    <!-- 历史问卷弹窗 -->
    <QuestionnaireHistoryModal ref="historyModalRef" @success="handleHistorySuccess" />

    <!-- 智能填写弹窗 -->
    <SmartFillModal ref="smartFillModalRef" @success="handleSmartFillSuccess" />

    <!-- 拍照组件 -->
    <CameraModal
      ref="cameraModalRef"
      @on-photo-taken="handlePhotoTaken"
      :constraints="{ video: { width: { ideal: 720 }, height: { ideal: 1280 } }, facingMode: 'environment' }"
    />
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, watch, provide, nextTick } from 'vue';
  import { getFileAccessHttpUrl } from '@/utils/common/compUtils';
  import defaultAvatar from '@/assets/images/defaultAvatar.png';
  import { saveOrUpdate, getByRegId } from '../ZyInquiry.api';
  import { Form, message, Modal } from 'ant-design-vue';
  const useForm = Form.useForm;
  import OccupationalHistoryList from './OccupationalHistoryList.vue';
  import RadiationHistoryList from './RadiationHistoryList.vue';
  import DiseaseHistoryList from './DiseaseHistoryList.vue';
  import FamilyHistoryList from './FamilyHistoryList.vue';
  import MaritalStatusList from './MaritalStatusList.vue';
  import SymptomList from './SymptomList.vue';
  import JDictSelectTag from '@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import QuestionnaireDisplayModal from './QuestionnaireDisplayModal.vue';
  import QuestionnaireDisplayContent from './QuestionnaireDisplayContent.vue';
  import QuestionnaireHistoryModal from './QuestionnaireHistoryModal.vue';
  import SmartFillModal from './SmartFillModal.vue';
  import CameraModal from '@/components/Camera/CameraModal.vue';
  import SignBoard from '@/components/SignBoard/SignBoard.vue';
  import { uploadFile } from '@/utils/upload';
  import {
    FileTextOutlined,
    HistoryOutlined,
    ThunderboltOutlined,
    CameraOutlined,
    EditOutlined,
    DeleteOutlined,
    CheckCircleOutlined,
  } from '@ant-design/icons-vue';

  // 组件属性
  interface Props {
    readonly?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    readonly: false,
  });

  // 事件定义
  const emit = defineEmits<{
    submit: [data: any];
    success: [];
    'update:readonly': [readonly: boolean];
  }>();

  // 弹窗相关状态
  const title = ref<string>('职业健康问卷');
  const width = ref<string>('100vw');
  const visible = ref<boolean>(false);
  const disableSubmit = computed(() => props.readonly);
  const currentCustomerReg = ref<any>(null);

  // 状态管理
  const submitting = ref(false);
  const activeTabKey = ref('basic');
  const loading = ref(false);
  const autoSaving = ref(false);

  // 问卷预览相关
  const questionnaireDisplayRef = ref();

  // 历史记录组件引用
  const occupationalHistoryRef = ref();
  const radiationHistoryRef = ref();
  const diseaseHistoryRef = ref();
  const familyHistoryRef = ref();
  const maritalStatusRef = ref();

  // 历史问卷相关
  const historyModalRef = ref();

  // 智能填写相关
  const smartFillModalRef = ref();

  // 拍照相关
  const cameraModalRef = ref();

  // 签字板相关
  const signBoardRef = ref();
  const signBoardConnected = ref(false);

  // 问卷完成状态相关
  const completingQuestionnaire = ref(false);
  const reopeningQuestionnaire = ref(false);
  const readonlyContentRef = ref();

  // 默认值对象
  const defaultFormData = {
    id: '',
    customerRegId: '',
    idCard: '', // 身份证号
    customerNo: '', // 体检号
    customerName: '', // 客户姓名
    customerGender: '', // 客户性别
    customerAge: '', // 客户年龄
    customerPhone: '', // 客户电话
    menarche: undefined,
    menstruation: undefined,
    period: '',
    menopauseAge: undefined,
    childCount: undefined,
    abortionCount: undefined,
    prematureCount: undefined,
    stillbirth: undefined,
    abnormalfetal: undefined,
    pregnancy: undefined,
    congenitalMalformations: '',
    smokStatus: '4',
    smokAmount: '0',
    smokYears: '0',
    drinkStatus: '不喝酒',
    drinkAmount: '0',
    drinkYears: '0',
    answerPicture: '', // 现场拍照图片URL - 对应数据库answer_picture字段
    signPicture: '', // 电子签名图片URL - 对应数据库sign_picture字段
    status: '暂存', // 问卷状态：暂存、已完成
  };

  // 简化的表单数据结构（参考ZyInquiryModal.vue）
  const formData = reactive({ ...defaultFormData });
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  //表单验证
  const validatorRules = reactive({
    smokStatus: [{ required: true, message: '请输入吸烟状态!' }],
    drinkStatus: [{ required: true, message: '请输入饮酒状态!' }],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: true });
  const formRef = ref();

  // 问卷状态
  const inquiryReady = ref(false);

  // 计算属性：判断是否为女性
  const isFemale = computed(() => {
    return currentCustomerReg.value?.gender === '女' || currentCustomerReg.value?.gender === '女性';
  });

  // 计算属性：问卷是否已完成
  const isCompleted = computed(() => {
    return formData.status === '已完成';
  });

  // 提供数据给子组件
  provide(
    'customerRegId',
    computed(() => currentCustomerReg.value?.id)
  );
  provide(
    'inquiryMainId',
    computed(() => formData.id)
  );
  provide(
    'inquiryReady',
    computed(() => inquiryReady.value)
  );
  provide(
    'customerReg',
    computed(() => currentCustomerReg.value)
  );

  /**
   * 打开弹窗
   */
  async function open(customerReg: any, readonly: boolean = false) {
    if (!customerReg || !customerReg.id) {
      message.error('请选择体检人！');
      return;
    }

    activeTabKey.value = 'basic';
    // disableSubmit.value = readonly; // 将由props.readonly替代
    currentCustomerReg.value = customerReg;
    visible.value = true;
    title.value = readonly ? '职业健康问卷（查看）' : '职业健康问卷';

    await loadData();
  }

  /**
   * 关闭弹窗
   */
  function handleCancel() {
    visible.value = false;
    emit('success');
  }

  // 保存数据
  async function handleSubmit() {
    if (!currentCustomerReg.value?.id) {
      message.error('缺少客户信息');
      return;
    }

    try {
      submitting.value = true;
      const submitData = {
        ...formData,
        customerRegId: currentCustomerReg.value.id,
        // 同步客户信息到问卷主表
        idCard: currentCustomerReg.value.idCard || '',
        customerNo: currentCustomerReg.value.customerNo || '',
        customerName: currentCustomerReg.value.name || '',
        customerGender: currentCustomerReg.value.gender || '',
        customerAge: currentCustomerReg.value.age ? `${currentCustomerReg.value.age}${currentCustomerReg.value.ageUnit || ''}` : '',
        customerPhone: currentCustomerReg.value.phone || '',
      };

      const isUpdate = !!formData.id;
      const result = await saveOrUpdate(submitData, isUpdate);

      if (result && result.success) {
        if (!isUpdate && result.result) {
          formData.id = result.result.id || result.result;
        }
        message.success('保存成功');
        emit('submit', submitData);
      } else {
        throw new Error(result?.message || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error(error.message || '保存失败');
    } finally {
      submitting.value = false;
    }
  }

  // 加载数据
  async function loadData() {
    if (!currentCustomerReg.value?.id) return;

    try {
      loading.value = true;
      const result = await getByRegId({ regId: currentCustomerReg.value.id });
      if (result && result.success && result.result) {
        if (result.result.newFlag == '1') {
          // 重置formData为默认值
          Object.assign(formData, defaultFormData);
        } else {
          Object.assign(formData, result.result);

          if (formData.smokStatus == null || formData.smokStatus === '') {
            formData.smokStatus = '4';
            formData.smokAmount = 0;
            formData.smokYears = 0;
            alert(1);
          }
          if (formData.drinkStatus == null || formData.drinkStatus === '') {
            formData.drinkStatus = '不喝酒';
            formData.drinkAmount = 0;
            formData.drinkYears = 0;
            alert(2);
          }
        }

        inquiryReady.value = true;
      } else {
        // 如果没有数据，创建新记录并同步客户信息
        formData.customerRegId = currentCustomerReg.value.id;
        syncCustomerInfo();
        inquiryReady.value = true;
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      // 即使加载失败，也允许用户填写新数据
      formData.customerRegId = currentCustomerReg.value.id;
      syncCustomerInfo();
      inquiryReady.value = true;
    } finally {
      loading.value = false;
    }
  }

  // 头像预览
  function previewAvatar() {
    const avatarUrl = currentCustomerReg.value?.avatar ? getFileAccessHttpUrl(currentCustomerReg.value.avatar) : defaultAvatar;

    // 创建图片预览弹窗
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      cursor: pointer;
    `;

    const img = document.createElement('img');
    img.src = avatarUrl;
    img.style.cssText = `
      max-width: 90%;
      max-height: 90%;
      border-radius: 8px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    `;

    modal.appendChild(img);
    document.body.appendChild(modal);

    // 点击关闭
    modal.addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    // ESC键关闭
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        document.body.removeChild(modal);
        document.removeEventListener('keydown', handleKeydown);
      }
    };
    document.addEventListener('keydown', handleKeydown);
  }

  // 预览问卷
  function handlePreviewQuestionnaire() {
    if (!currentCustomerReg.value) {
      message.error('缺少客户信息');
      return;
    }

    questionnaireDisplayRef.value?.open(currentCustomerReg.value, formData.id);
  }

  // 预览成功回调
  function handlePreviewSuccess() {
    console.log('问卷预览完成');
  }

  // 同步客户信息到表单数据
  function syncCustomerInfo() {
    if (currentCustomerReg.value) {
      formData.idCard = currentCustomerReg.value.idCard || '';
      formData.customerNo = currentCustomerReg.value.customerNo || '';
      formData.customerName = currentCustomerReg.value.name || '';
      formData.customerGender = currentCustomerReg.value.gender || '';
      formData.customerAge = currentCustomerReg.value.age ? `${currentCustomerReg.value.age}${currentCustomerReg.value.ageUnit || ''}` : '';
      formData.customerPhone = currentCustomerReg.value.phone || '';
    }
  }

  // 显示历史问卷
  function handleShowHistory() {
    if (!currentCustomerReg.value) {
      message.error('缺少客户信息');
      return;
    }

    historyModalRef.value?.open(currentCustomerReg.value, formData.id);
  }

  // 历史问卷成功回调
  function handleHistorySuccess() {
    console.log('历史问卷操作完成，刷新页面数据');
    // 重新加载问卷数据
    loadData();
  }

  // 复制历史数据到当前问卷
  function handleCopyData(historyData: any) {
    // 复制基本信息，但保留当前的客户关联信息
    const currentCustomerRegId = formData.customerRegId;
    const currentId = formData.id;
    const currentCustomerInfo = {
      idCard: formData.idCard,
      customerNo: formData.customerNo,
      customerName: formData.customerName,
      customerGender: formData.customerGender,
      customerAge: formData.customerAge,
      customerPhone: formData.customerPhone,
    };

    // 复制历史数据
    Object.assign(formData, historyData, {
      // 保留当前记录的关键信息
      id: currentId,
      customerRegId: currentCustomerRegId,
      ...currentCustomerInfo,
    });

    message.success('历史数据复制成功，请检查并保存');
  }

  // 显示智能填写
  function handleSmartFill() {
    if (!currentCustomerReg.value) {
      message.error('缺少客户信息');
      return;
    }

    if (!currentCustomerReg.value.idCard) {
      message.error('客户身份证号信息缺失，无法进行智能推荐');
      return;
    }

    smartFillModalRef.value?.open(currentCustomerReg.value);
  }

  // 智能填写成功回调
  function handleSmartFillSuccess() {
    console.log('智能填写操作完成，刷新页面数据');
    // 重新加载问卷数据
    loadData();
  }

  // 拍照处理
  function handleTakePhoto() {
    if (!currentCustomerReg.value) {
      message.error('缺少客户信息');
      return;
    }
    cameraModalRef.value?.open();
  }

  // 拍照完成回调
  async function handlePhotoTaken(data: any) {
    try {
      if (!data || !data.blob) {
        message.error('拍照失败，请重试');
        return;
      }

      // 上传图片到MinIO
      const uploadRes = await uploadFile(data.blob, 'jpg');
      if (uploadRes && uploadRes.message) {
        // 更新到answer_picture字段
        formData.answerPicture = uploadRes.message;
        message.success('拍照成功，图片已上传到文件服务器');

        // 自动保存到数据库
        if (formData.id) {
          await handleSubmit();
        }
      } else {
        message.error('图片上传到文件服务器失败');
      }
    } catch (error) {
      console.error('拍照处理失败:', error);
      message.error('拍照处理失败: ' + (error.message || '未知错误'));
    }
  }

  // 删除照片
  function handleDeletePhoto() {
    formData.answerPicture = '';
    message.success('照片已删除');
    // 自动保存
    if (formData.id) {
      handleSubmit();
    }
  }

  // 签字处理
  function handleSign() {
    if (!currentCustomerReg.value) {
      message.error('缺少客户信息');
      return;
    }
    if (!signBoardConnected.value) {
      message.error('签字板未连接，请检查设备');
      return;
    }
    signBoardRef.value?.startSign();
  }

  // 签字完成回调
  async function handleSignDone(base64Data: any) {
    try {
      if (!base64Data || !base64Data.img) {
        message.error('签名失败，请重试');
        return;
      }

      // 将base64转换为blob并上传到MinIO
      const base64String = base64Data.img;
      const byteCharacters = atob(base64String.split(',')[1]);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: 'image/png' });

      // 上传签名图片到MinIO
      const uploadRes = await uploadFile(blob, 'png');
      if (uploadRes && uploadRes.message) {
        // 更新到sign_picture字段
        formData.signPicture = uploadRes.message;
        message.success('签名成功，图片已上传到文件服务器');

        // 自动保存到数据库
        if (formData.id) {
          await handleSubmit();
        }
      } else {
        message.error('签名上传到文件服务器失��');
      }
    } catch (error) {
      console.error('签名处理失败:', error);
      message.error('签名处理失败: ' + (error.message || '未知错误'));
    }
  }

  // 删除签名
  function handleDeleteSignature() {
    formData.signPicture = '';
    message.success('签名已删除');
    // 自动保存
    if (formData.id) {
      handleSubmit();
    }
  }

  // 签字板连接状态回调
  function handleSignBoardConnected() {
    signBoardConnected.value = true;
    message.success('签字板已连接');
  }

  // 获取当前时间
  function getCurrentTime() {
    return new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  }

  // 获取状态文本
  function getStatusText() {
    if (isCompleted.value) {
      return '问卷已完成';
    } else if (disableSubmit.value) {
      return '查看模式';
    } else {
      return '编辑模式';
    }
  }

  // 验证问卷完整性
  function validateQuestionnaireCompleteness() {
    const errors = [];

    // 检查基本信息
    if (!formData.smokStatus) {
      errors.push('请选择吸烟状态');
    }
    if (!formData.drinkStatus) {
      errors.push('请选择饮酒状态');
    }

    // 检查必要的子问卷数据（这里可以根据业务需求添加更多验证）
    // 例如：职业史、症状等

    return errors;
  }

  // 完成问卷
  async function handleCompleteQuestionnaire() {
    try {
      // 验证问卷完整性
      const validationErrors = validateQuestionnaireCompleteness();
      if (validationErrors.length > 0) {
        message.warning(`问卷信息不完整：\n${validationErrors.join('\n')}`);
        return;
      }

      // 确认对话框
      const confirmed = await new Promise((resolve) => {
        Modal.confirm({
          title: '确认完成问卷',
          content: '完成后问卷将被锁定，切换为只读模式。是否确认完成？',
          okText: '确认完成',
          cancelText: '取消',
          onOk: () => resolve(true),
          onCancel: () => resolve(false),
        });
      });

      if (!confirmed) return;

      completingQuestionnaire.value = true;

      // 更新状态为已完成
      formData.status = '已完成';

      // 保存到数据库
      await handleSubmit();

      // 切换到只读模式
      emit('update:readonly', true);

      message.success('问卷已完成并切换为只读模式！');

      // 切换组件后，立即调用其 open 方法加载数据
      /*nextTick(() => {
        if (readonlyContentRef.value) {
          readonlyContentRef.value.open();
        }
      });*/
    } catch (error) {
      console.error('完成问卷失败:', error);
      message.error('完成问卷失败: ' + (error.message || '未知错误'));
      // 恢复状态
      formData.status = '暂存';
    } finally {
      completingQuestionnaire.value = false;
    }
  }

  // 重新编辑问卷
  async function handleReopenQuestionnaire() {
    try {
      // 确认对话框
      const confirmed = await new Promise((resolve) => {
        Modal.confirm({
          title: '确认重新编辑',
          content: '重新编辑将解锁问卷，切换为编辑模式。是否确认？',
          okText: '确认重新编辑',
          cancelText: '取消',
          onOk: () => resolve(true),
          onCancel: () => resolve(false),
        });
      });

      if (!confirmed) return;

      reopeningQuestionnaire.value = true;

      // 更新状态为暂存
      formData.status = '暂存';

      // 保存到数据库
      //await handleSubmit();

      // 切换到编辑模式
      emit('update:readonly', false);

      message.success('问卷已重新开放编辑！');
    } catch (error) {
      console.error('重新编辑问卷失败:', error);
      message.error('重新编辑问卷失败: ' + (error.message || '未知错误'));
      // 恢复状态
      formData.status = '已完成';
    } finally {
      reopeningQuestionnaire.value = false;
    }
  }

  // 清空所有子问卷数据
  async function clearAllSubQuestionnaires() {
    // 这里需要调用各个子问卷的清空方法
    // 具体实现需要根据子组件的API来调整
    console.log('清空所有子问卷数据');
  }

  // 自动保存
  let autoSaveTimer: NodeJS.Timeout | null = null;
  watch(
    () => formData,
    () => {
      if (!inquiryReady.value || disableSubmit.value) return;

      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer);
      }

      autoSaveTimer = setTimeout(async () => {
        try {
          autoSaving.value = true;
          const isUpdate = !!formData.id;
          const result = await saveOrUpdate({ ...formData }, isUpdate);

          if (result && result.success && !isUpdate && result.result) {
            formData.id = result.result.id || result.result;
          }
        } catch (error) {
          console.error('自动保存失败:', error);
        } finally {
          autoSaving.value = false;
        }
      }, 2000);
    },
    { deep: true }
  );

  // 监听吸烟状态变化
  watch(
    () => formData.smokStatus,
    (value: string) => {
      console.log('吸烟状态变化:', value); // 添加调试日志
      if (value == 4) {
        formData.smokAmount = '0';
        formData.smokYears = '0';
      }
    },
    { immediate: false } // 添加配置选项
  );

  // 监听饮酒状态变化
  watch(
    () => formData.drinkStatus,
    (value: string) => {
      console.log('饮酒状态变化:', value); // 添加调试日志
      if (value && (value.includes('不') || value.includes('戒酒'))) {
        formData.drinkAmount = '0';
        formData.drinkYears = '0';
      }
    },
    { immediate: false } // 添加配置选项
  );

  // 监听 tab 切换，自动添加空卡片
  watch(
    () => activeTabKey.value,
    async (newTabKey: string) => {
      // 只在非只读模式下执行
      if (disableSubmit.value) return;

      // 使用 nextTick 确保组件已经渲染完成
      await nextTick();

      try {
        switch (newTabKey) {
          case 'occupation':
            await occupationalHistoryRef.value?.addEmptyCard();
            break;
          case 'radiation':
            await radiationHistoryRef.value?.addEmptyCard();
            break;
          case 'disease':
            await diseaseHistoryRef.value?.addEmptyCard();
            break;
          case 'family':
            await familyHistoryRef.value?.addEmptyCard();
            break;
          case 'marital':
            await maritalStatusRef.value?.addEmptyCard();
            break;
        }
      } catch (error) {
        console.error('添加空卡片时出错:', error);
      }
    }
  );

  defineExpose({
    open,
  });
</script>

<style lang="less" scoped>
  .comprehensive-inquiry {
    // 客户信息区域 - 简洁大方的���部设计
    .customer-info {
      display: flex;
      align-items: center;
      gap: 20px;
      margin-bottom: 8px;
      padding: 10px;
      border-radius: 4px;
      color: #495057;
      border: 1px solid #e9ecef;

      .customer-avatar {
        position: relative;
        flex-shrink: 0;

        .avatar {
          width: 48px;
          height: 48px;
          border-radius: 4px;
          background-size: cover;
          background-position: center;
          background-color: #f8f9fa;
          border: 2px solid #dee2e6;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.02);
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
          }
        }
      }

      .info {
        flex: 1;
        min-width: 0;

        h3 {
          margin: 0 0 12px 0;
          font-size: 16px;
          font-weight: 600;
          color: #212529;
        }

        .details {
          display: flex;
          gap: 20px;
          flex-wrap: wrap;

          span {
            display: flex;
            align-items: center;
            gap: 4px;
            white-space: nowrap;
            font-size: 14px;
            color: #6c757d;

            .detail-label {
              font-weight: 500;
              color: #495057;
              min-width: fit-content;
            }
          }
        }
      }

      .status {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;

        .status-indicator {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #52c41a;
          box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
          animation: pulse 2s infinite;

          &.readonly {
            background: #faad14;
            box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
          }

          &.completed {
            background: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            animation: completedPulse 2s infinite;
          }
        }

        span {
          font-size: 14px;
          color: #6c757d;
        }
      }

      .action-buttons {
        flex-shrink: 0;
        margin-left: 16px;
      }
    }

    // 问卷标题区域
    .section-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 20px;
      padding: 16px 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      border-left: 4px solid #1890ff;

      span {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
      }
    }

    // 垂直标签页
    .tab-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      overflow: hidden;

      .vertical-tabs {
        :deep(.ant-tabs-nav) {
          width: 36px;
          background: #fafafa;
          border-right: 1px solid #f0f0f0;
        }

        :deep(.ant-tabs-content-holder) {
          margin-left: 0;
          flex: 1;
          background: white;
        }

        :deep(.ant-tabs-tab) {
          padding: 16px 8px;
          margin: 0;
          border-radius: 0;
          border-bottom: 1px solid #f0f0f0;

          &.ant-tabs-tab-active {
            background: white;
            border-right: 2px solid #1890ff;

            .vertical-tab-label-optimized {
              color: #1890ff;
              font-weight: 600;
            }
          }
        }
      }

      .vertical-tab-label-optimized {
        writing-mode: vertical-rl;
        text-orientation: upright;
        width: 100%;
        text-align: center;
        font-size: 14px;
        color: #595959;
        transition: all 0.3s;
      }

      .tab-content {
        min-height: 600px;
        padding: 8px;
        background: white;
      }
    }

    // 缩略图展示样式
    .thumbnail-row {
      .thumbnail-item {
        .thumbnail-container {
          position: relative;
          cursor: pointer;
          border-radius: 6px;
          overflow: hidden;
          transition: all 0.3s ease;
          border: 2px solid #f0f0f0;

          &:hover {
            border-color: #1890ff;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
          }

          .thumbnail-image {
            width: 60px;
            height: 40px;
            object-fit: cover;
            display: block;

            &.signature-thumb {
              background: white;
            }
          }

          .thumbnail-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          }
        }
      }
    }

    // 现场记录tab样式
    .records-content {
      padding: 20px;

      .records-actions {
        .action-card {
          width: 300px;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

          .action-content {
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .empty-state {
              text-align: center;
              padding: 20px;

              .ant-btn {
                border-radius: 6px;
                font-weight: 500;

                &:hover {
                  transform: translateY(-1px);
                  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
                }
              }
            }

            .photo-display,
            .signature-display {
              text-align: center;
              width: 100%;

              .record-image {
                width: 100%;
                max-width: 200px;
                max-height: 120px;
                object-fit: contain;
                border-radius: 6px;
                border: 1px solid #f0f0f0;

                &.signature-image {
                  background: white;
                  padding: 8px;
                }
              }

              .image-info {
                margin-top: 12px;

                .ant-btn {
                  border-radius: 4px;

                  &:hover {
                    transform: translateY(-1px);
                  }
                }
              }
            }
          }
        }
      }

      .signboard-status {
        .ant-card {
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
      }
    }

    .form-card {
      margin-bottom: 20px;
      border-radius: 8px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
      border: 1px solid #f0f0f0;
      overflow: visible;
    }

    // 表单操作区域
    .form-actions {
      text-align: right;
      margin-top: 24px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
      background: white;
    }
  }

  // 动画效果
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4);
    }
    70% {
      box-shadow: 0 0 0 6px rgba(82, 196, 26, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
    }
  }

  @keyframes completedPulse {
    0% {
      box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
    }
    70% {
      box-shadow: 0 0 0 6px rgba(24, 144, 255, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
    }
  }
</style>

<!-- 全屏弹窗样式 -->
<style lang="less" scoped>
  // 全屏内容区域优化
  .fullscreen-content {
    .tab-container,
    .readonly-content {
      height: calc(100vh - 200px); // 减去头部和其他元素的高度
      overflow-y: auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    .readonly-content {
      padding: 16px;
    }

    .tab-container {
      .ant-tabs {
        height: 100%;

        .ant-tabs-content-holder {
          height: calc(100% - 46px); // 减去tab头部高度

          .ant-tabs-content {
            height: 100%;

            .ant-tabs-tabpane {
              height: 100%;
              overflow-y: auto;
            }
          }
        }
      }
    }
  }
</style>
